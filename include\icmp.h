#ifndef ICMP_H
#define ICMP_H

#include "net.h"

#pragma pack(1)
typedef struct icmp_hdr {
    uint8_t type;         // 类型
    uint8_t code;         // 代码
    uint16_t checksum16;  // ICMP报文的校验和
    uint16_t id16;        // 标识符
    uint16_t seq16;       // 序号
} icmp_hdr_t;

#pragma pack()
typedef enum icmp_type {
    ICMP_TYPE_ECHO_REQUEST = 8,  // 回显请求
    ICMP_TYPE_ECHO_REPLY = 0,    // 回显响应
    ICMP_TYPE_UNREACH = 3,       // 目的不可达
} icmp_type_t;

typedef enum icmp_code {
    ICMP_CODE_PROTOCOL_UNREACH = 2,  // 协议不可达
    ICMP_CODE_PORT_UNREACH = 3       // 端口不可达
} icmp_code_t;

// PING相关数据结构
typedef struct icmp_ping_record {
    uint16_t id;           // ICMP ID
    uint16_t seq;          // 序列号
    uint8_t target_ip[NET_IP_LEN];  // 目标IP
    double send_time;      // 发送时间戳(毫秒)
} icmp_ping_record_t;

typedef struct icmp_ping_stats {
    int total_sent;        // 总发送数
    int total_received;    // 总接收数
    double min_time;       // 最小响应时间(ms)
    double max_time;       // 最大响应时间(ms)
    double total_time;     // 总响应时间(ms)
} icmp_ping_stats_t;

// PING配置
#define ICMP_PING_TIMEOUT 5    // PING超时时间(秒)
#define ICMP_PING_COUNT 4      // PING次数
#define ICMP_PING_INTERVAL 1   // PING间隔(秒)
#define ICMP_PING_DATA_SIZE 32 // PING数据大小

// 函数声明
void icmp_in(buf_t *buf, uint8_t *src_ip);
void icmp_unreachable(buf_t *recv_buf, uint8_t *src_ip, icmp_code_t code);
void icmp_init();

// PING相关函数
void icmp_ping_init();
void icmp_ping_request(uint8_t *target_ip);
void icmp_ping_poll();
void icmp_ping_print_stats();

#endif
