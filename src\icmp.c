#include "icmp.h"

#include "ip.h"
#include "net.h"
#include "map.h"

#include <stdio.h>
#include <string.h>
#include <time.h>
#ifdef _WIN32
#include <windows.h>
#else
#include <sys/time.h>
#endif

// PING相关全局变量
static map_t icmp_ping_table;  // PING请求记录表
static icmp_ping_stats_t ping_stats;  // PING统计信息
static uint8_t ping_target_ip[NET_IP_LEN];  // 目标IP
static uint16_t ping_id;  // PING ID
static uint16_t ping_seq;  // 当前序列号
static time_t last_ping_time;  // 上次发送PING的时间
static int ping_count_sent;  // 已发送的PING数量
static int ping_active = 0;  // PING是否激活

// 获取高精度时间戳（毫秒）
static double get_time_ms() {
#ifdef _WIN32
    static LARGE_INTEGER frequency = {0};
    LARGE_INTEGER counter;

    if (frequency.QuadPart == 0) {
        QueryPerformanceFrequency(&frequency);
    }

    QueryPerformanceCounter(&counter);
    return (double)(counter.QuadPart * 1000.0) / frequency.QuadPart;
#else
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return tv.tv_sec * 1000.0 + tv.tv_usec / 1000.0;
#endif
}

/**
 * @brief 发送icmp响应
 *
 * @param req_buf 收到的icmp请求包
 * @param src_ip 源ip地址
 */
static void icmp_resp(buf_t *req_buf, uint8_t *src_ip) {
    // Step1: 初始化并封装数据
    // 初始化发送缓冲区
    buf_init(&txbuf, req_buf->len);
    
    // 拷贝接收到的ICMP数据
    memcpy(txbuf.data, req_buf->data, req_buf->len);
    
    // 获取ICMP头部
    icmp_hdr_t *req_icmp_hdr = (icmp_hdr_t *)req_buf->data;
    icmp_hdr_t *resp_icmp_hdr = (icmp_hdr_t *)txbuf.data;
    
    // 修改ICMP类型为回显应答
    resp_icmp_hdr->type = ICMP_TYPE_ECHO_REPLY;
    resp_icmp_hdr->code = 0;
    
    // Step2: 填写校验和
    // 先将校验和字段清零
    resp_icmp_hdr->checksum16 = 0;
    // 计算校验和
    resp_icmp_hdr->checksum16 = checksum16((uint16_t *)txbuf.data, txbuf.len);
    
    // Step3: 发送数据报
    ip_out(&txbuf, src_ip, NET_PROTOCOL_ICMP);
}

/**
 * @brief 处理一个收到的数据包
 *
 * @param buf 要处理的数据包
 * @param src_ip 源ip地址
 */
void icmp_in(buf_t *buf, uint8_t *src_ip) {
    // Step1: 报头检测
    if (buf->len < sizeof(icmp_hdr_t)) {
        // 数据包不完整,直接丢弃
        return;
    }

    // 获取ICMP头部
    icmp_hdr_t *icmp_hdr = (icmp_hdr_t *)buf->data;

    // 调试信息
    printf("DEBUG: Received ICMP packet from %s, type=%u, code=%u, id=%u, seq=%u\n",
           iptos(src_ip), icmp_hdr->type, icmp_hdr->code,
           swap16(icmp_hdr->id16), swap16(icmp_hdr->seq16));

    // Step2: 查看ICMP类型
    // Step3: 如果是回显请求,则回送回显应答
    if (icmp_hdr->type == ICMP_TYPE_ECHO_REQUEST) {
        icmp_resp(buf, src_ip);
    }
    // Step4: 如果是回显应答,处理PING回复
    else if (icmp_hdr->type == ICMP_TYPE_ECHO_REPLY && ping_active) {
        // 检查是否是我们发送的PING请求的回复
        uint16_t reply_id = swap16(icmp_hdr->id16);
        uint16_t reply_seq = swap16(icmp_hdr->seq16);

        if (reply_id == ping_id && memcmp(src_ip, ping_target_ip, NET_IP_LEN) == 0) {
            // 构造查找键（只使用seq作为键）
            uint16_t key = reply_seq;

            // 查找对应的请求记录
            icmp_ping_record_t *record = map_get(&icmp_ping_table, &key);
            if (record) {
                // 计算响应时间
                double current_time = get_time_ms();
                double response_time = current_time - record->send_time;

                // 更新统计信息
                ping_stats.total_received++;
                ping_stats.total_time += response_time;

                if (ping_stats.total_received == 1) {
                    ping_stats.min_time = ping_stats.max_time = response_time;
                } else {
                    if (response_time < ping_stats.min_time) {
                        ping_stats.min_time = response_time;
                    }
                    if (response_time > ping_stats.max_time) {
                        ping_stats.max_time = response_time;
                    }
                }

                // 打印PING结果
                printf("64 bytes from %s: icmp_seq=%u time=%.1f ms\n",
                       iptos(src_ip), reply_seq, response_time);

                // 删除记录
                map_delete(&icmp_ping_table, &key);
            }
        }
    }
}

/**
 * @brief 发送icmp不可达
 *
 * @param recv_buf 收到的ip数据包
 * @param src_ip 源ip地址
 * @param code icmp code，协议不可达或端口不可达
 */
void icmp_unreachable(buf_t *recv_buf, uint8_t *src_ip, icmp_code_t code) {
    // Step1: 初始化并填写报头
    // ICMP差错报文需要包含IP头部和IP数据的前8个字节
    // 计算需要的缓冲区大小: ICMP头部 + IP头部 + 8字节数据
    size_t ip_hdr_len = sizeof(ip_hdr_t);
    size_t data_len = 8; // ICMP差错报文数据字段包含IP数据报的前8个字节
    
    // 初始化发送缓冲区
    buf_init(&txbuf, sizeof(icmp_hdr_t) + ip_hdr_len + data_len);
    
    // 填写ICMP头部
    icmp_hdr_t *icmp_hdr = (icmp_hdr_t *)txbuf.data;
    icmp_hdr->type = ICMP_TYPE_UNREACH;
    icmp_hdr->code = code;
    icmp_hdr->id16 = 0;
    icmp_hdr->seq16 = 0;
    
    // Step2: 填写数据与校验和
    // 拷贝IP头部和数据的前8个字节到ICMP报文的数据字段
    // ICMP头部后面是IP头部和数据
    uint8_t *icmp_data = txbuf.data + sizeof(icmp_hdr_t);
    
    // 拷贝IP头部
    memcpy(icmp_data, recv_buf->data, ip_hdr_len);
    
    // 拷贝IP数据的前8个字节(如果有)
    if (recv_buf->len > ip_hdr_len) {
        size_t copy_len = (recv_buf->len - ip_hdr_len) < data_len ? 
                          (recv_buf->len - ip_hdr_len) : data_len;
        memcpy(icmp_data + ip_hdr_len, recv_buf->data + ip_hdr_len, copy_len);
    }
    
    // 计算校验和
    icmp_hdr->checksum16 = 0;
    icmp_hdr->checksum16 = checksum16((uint16_t *)txbuf.data, txbuf.len);
    
    // Step3: 发送数据报
    ip_out(&txbuf, src_ip, NET_PROTOCOL_ICMP);
}

/**
 * @brief 初始化icmp协议
 *
 */
void icmp_init() {
    net_add_protocol(NET_PROTOCOL_ICMP, icmp_in);
}

/**
 * @brief 初始化PING功能
 *
 */
void icmp_ping_init() {
    // 初始化PING记录表，使用seq作为键，设置超时时间
    map_init(&icmp_ping_table, sizeof(uint16_t), sizeof(icmp_ping_record_t),
             0, ICMP_PING_TIMEOUT, NULL, NULL);

    // 初始化统计信息
    memset(&ping_stats, 0, sizeof(icmp_ping_stats_t));

    // 初始化其他变量
    ping_id = (uint16_t)(time(NULL) & 0xFFFF);  // 使用时间戳作为ID
    ping_seq = 0;
    last_ping_time = 0;
    ping_count_sent = 0;
    ping_active = 0;
}

/**
 * @brief 发送ICMP请求
 *
 * @param target_ip 目标IP地址
 */
void icmp_ping_request(uint8_t *target_ip) {
    if (!ping_active) {
        // 开始新的PING会话
        memcpy(ping_target_ip, target_ip, NET_IP_LEN);
        ping_active = 1;
        ping_count_sent = 0;
        ping_seq = 0;
        memset(&ping_stats, 0, sizeof(icmp_ping_stats_t));

        printf("PING %s: %d data bytes\n", iptos(target_ip), ICMP_PING_DATA_SIZE);
    }

    if (ping_count_sent >= ICMP_PING_COUNT) {
        return;  // 已发送足够数量的请求
    }

    // 初始化发送缓冲区
    size_t total_len = sizeof(icmp_hdr_t) + ICMP_PING_DATA_SIZE;
    buf_init(&txbuf, total_len);

    // 填写ICMP头部
    icmp_hdr_t *icmp_hdr = (icmp_hdr_t *)txbuf.data;
    icmp_hdr->type = ICMP_TYPE_ECHO_REQUEST;
    icmp_hdr->code = 0;
    icmp_hdr->id16 = swap16(ping_id);
    icmp_hdr->seq16 = swap16(ping_seq);

    // 填写数据部分（简单的模式数据）
    uint8_t *data = txbuf.data + sizeof(icmp_hdr_t);
    for (int i = 0; i < ICMP_PING_DATA_SIZE; i++) {
        data[i] = (uint8_t)(0x08 + i);  // 简单的测试数据模式
    }

    // 计算校验和
    icmp_hdr->checksum16 = 0;
    icmp_hdr->checksum16 = checksum16((uint16_t *)txbuf.data, txbuf.len);

    // 记录请求信息
    icmp_ping_record_t record;
    record.id = ping_id;
    record.seq = ping_seq;
    memcpy(record.target_ip, target_ip, NET_IP_LEN);
    record.send_time = get_time_ms();

    // 将记录添加到表中，使用seq作为键
    map_set(&icmp_ping_table, &ping_seq, &record);

    // 发送ICMP请求
    ip_out(&txbuf, target_ip, NET_PROTOCOL_ICMP);

    // 打印发送信息
    printf("PING %s: seq=%u id=%u\n", iptos(target_ip), ping_seq, ping_id);

    // 更新统计和状态
    ping_stats.total_sent++;
    ping_count_sent++;
    ping_seq++;
    last_ping_time = time(NULL);
}

/**
 * @brief PING轮询函数，处理定时发送和超时检查
 *
 */
void icmp_ping_poll() {
    if (!ping_active) {
        return;
    }

    time_t current_time = time(NULL);

    // 检查是否需要发送下一个PING请求
    if (ping_count_sent < ICMP_PING_COUNT &&
        (last_ping_time == 0 || current_time - last_ping_time >= ICMP_PING_INTERVAL)) {
        icmp_ping_request(ping_target_ip);
    }

    // 检查是否所有请求都已发送且等待时间足够
    if (ping_count_sent >= ICMP_PING_COUNT &&
        current_time - last_ping_time >= ICMP_PING_TIMEOUT) {
        // 结束PING会话
        icmp_ping_print_stats();
        ping_active = 0;
    }
}

/**
 * @brief 打印PING统计信息
 *
 */
void icmp_ping_print_stats() {
    printf("\n--- %s ping statistics ---\n", iptos(ping_target_ip));
    printf("%d packets transmitted, %d packets received, %.1f%% packet loss\n",
           ping_stats.total_sent, ping_stats.total_received,
           ping_stats.total_sent > 0 ?
           (double)(ping_stats.total_sent - ping_stats.total_received) * 100.0 / ping_stats.total_sent : 0.0);

    if (ping_stats.total_received > 0) {
        double avg_time = ping_stats.total_time / ping_stats.total_received;
        printf("round-trip min/avg/max = %.1f/%.1f/%.1f ms\n",
               ping_stats.min_time, avg_time, ping_stats.max_time);
    }
}
