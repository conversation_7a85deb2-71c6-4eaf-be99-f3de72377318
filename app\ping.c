#include "driver.h"
#include "net.h"
#include "icmp.h"

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// 将IP字符串转换为字节数组
int str_to_ip(const char *ip_str, uint8_t *ip) {
    int a, b, c, d;
    if (sscanf(ip_str, "%d.%d.%d.%d", &a, &b, &c, &d) != 4) {
        return -1;
    }
    if (a < 0 || a > 255 || b < 0 || b > 255 || 
        c < 0 || c > 255 || d < 0 || d > 255) {
        return -1;
    }
    ip[0] = (uint8_t)a;
    ip[1] = (uint8_t)b;
    ip[2] = (uint8_t)c;
    ip[3] = (uint8_t)d;
    return 0;
}

int main(int argc, char const *argv[]) {
    // 检查命令行参数
    if (argc != 2) {
        printf("Usage: %s <target_ip>\n", argv[0]);
        printf("Example: %s *******\n", argv[0]);
        return -1;
    }

    // 解析目标IP地址
    uint8_t target_ip[NET_IP_LEN];
    if (str_to_ip(argv[1], target_ip) != 0) {
        printf("Invalid IP address: %s\n", argv[1]);
        return -1;
    }

    // 初始化协议栈
    if (net_init() == -1) {
        printf("net init failed.\n");
        return -1;
    }

    // 初始化PING功能
    icmp_ping_init();

    // 开始PING
    icmp_ping_request(target_ip);

    // 主循环
    while (1) {
        net_poll();        // 处理网络数据包
        icmp_ping_poll();  // 处理PING逻辑
    }

    return 0;
}
